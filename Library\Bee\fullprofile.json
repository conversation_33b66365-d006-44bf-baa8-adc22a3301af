{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 17020, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 17020, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 17020, "tid": 16, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 17020, "tid": 16, "ts": 1750355167829885, "dur": 598, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 17020, "tid": 16, "ts": 1750355167830499, "dur": 5, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 17020, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 17020, "tid": 1, "ts": 1750355167608708, "dur": 4718, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 17020, "tid": 1, "ts": 1750355167613431, "dur": 25026, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 17020, "tid": 1, "ts": 1750355167638460, "dur": 26897, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 17020, "tid": 16, "ts": 1750355167830507, "dur": 11, "ph": "X", "name": "", "args": {}}, {"pid": 17020, "tid": 34359738368, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167608662, "dur": 23880, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167632543, "dur": 193032, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167632555, "dur": 42, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167632600, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167632603, "dur": 423, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167633030, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167633034, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167633084, "dur": 12, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167633097, "dur": 2062, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167635168, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167635172, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167635236, "dur": 2, "ph": "X", "name": "ProcessMessages 1312", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167635239, "dur": 93, "ph": "X", "name": "ReadAsync 1312", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167635335, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167635336, "dur": 63, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167635404, "dur": 3, "ph": "X", "name": "ProcessMessages 1472", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167635408, "dur": 54, "ph": "X", "name": "ReadAsync 1472", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167635465, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167635467, "dur": 41, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167635512, "dur": 1, "ph": "X", "name": "ProcessMessages 946", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167635515, "dur": 79, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167635599, "dur": 100, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167635702, "dur": 2, "ph": "X", "name": "ProcessMessages 1117", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167635705, "dur": 749, "ph": "X", "name": "ReadAsync 1117", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167636456, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167636458, "dur": 351, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167636811, "dur": 2, "ph": "X", "name": "ProcessMessages 2666", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167636814, "dur": 41, "ph": "X", "name": "ReadAsync 2666", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167636858, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167636860, "dur": 86, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167636950, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167636998, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167636999, "dur": 39, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167637045, "dur": 2, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167637048, "dur": 107, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167637159, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167637162, "dur": 102, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167637267, "dur": 3, "ph": "X", "name": "ProcessMessages 3383", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167637271, "dur": 49, "ph": "X", "name": "ReadAsync 3383", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167637325, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167637328, "dur": 467, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167637800, "dur": 2, "ph": "X", "name": "ProcessMessages 876", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167637803, "dur": 72, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167637879, "dur": 1, "ph": "X", "name": "ProcessMessages 1148", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167637881, "dur": 103, "ph": "X", "name": "ReadAsync 1148", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167637988, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167637991, "dur": 176, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638169, "dur": 2, "ph": "X", "name": "ProcessMessages 1768", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638172, "dur": 43, "ph": "X", "name": "ReadAsync 1768", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638220, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638223, "dur": 265, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638491, "dur": 1, "ph": "X", "name": "ProcessMessages 1152", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638494, "dur": 39, "ph": "X", "name": "ReadAsync 1152", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638536, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638538, "dur": 30, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638570, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638573, "dur": 39, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638615, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638617, "dur": 37, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638656, "dur": 1, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638657, "dur": 46, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638706, "dur": 29, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638737, "dur": 30, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638770, "dur": 31, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638802, "dur": 1, "ph": "X", "name": "ProcessMessages 925", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638804, "dur": 27, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638833, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638835, "dur": 33, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638870, "dur": 1, "ph": "X", "name": "ProcessMessages 933", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638872, "dur": 93, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638969, "dur": 1, "ph": "X", "name": "ProcessMessages 899", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167638971, "dur": 67, "ph": "X", "name": "ReadAsync 899", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639042, "dur": 2, "ph": "X", "name": "ProcessMessages 2393", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639046, "dur": 48, "ph": "X", "name": "ReadAsync 2393", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639097, "dur": 1, "ph": "X", "name": "ProcessMessages 1244", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639100, "dur": 82, "ph": "X", "name": "ReadAsync 1244", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639184, "dur": 1, "ph": "X", "name": "ProcessMessages 1002", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639186, "dur": 45, "ph": "X", "name": "ReadAsync 1002", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639234, "dur": 1, "ph": "X", "name": "ProcessMessages 1784", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639236, "dur": 30, "ph": "X", "name": "ReadAsync 1784", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639269, "dur": 32, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639303, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639304, "dur": 27, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639333, "dur": 1, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639335, "dur": 34, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639372, "dur": 32, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639408, "dur": 29, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639441, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639443, "dur": 91, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639539, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639541, "dur": 49, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639593, "dur": 1, "ph": "X", "name": "ProcessMessages 874", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639595, "dur": 49, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639648, "dur": 1, "ph": "X", "name": "ProcessMessages 942", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639650, "dur": 53, "ph": "X", "name": "ReadAsync 942", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639706, "dur": 1, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639708, "dur": 71, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639783, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639838, "dur": 1, "ph": "X", "name": "ProcessMessages 961", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639840, "dur": 43, "ph": "X", "name": "ReadAsync 961", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639886, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639889, "dur": 58, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639951, "dur": 1, "ph": "X", "name": "ProcessMessages 154", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167639953, "dur": 65, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640022, "dur": 2, "ph": "X", "name": "ProcessMessages 1271", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640025, "dur": 43, "ph": "X", "name": "ReadAsync 1271", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640071, "dur": 2, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640074, "dur": 58, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640135, "dur": 2, "ph": "X", "name": "ProcessMessages 893", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640139, "dur": 53, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640196, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640198, "dur": 47, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640247, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640250, "dur": 46, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640299, "dur": 1, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640301, "dur": 67, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640371, "dur": 1, "ph": "X", "name": "ProcessMessages 1174", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640373, "dur": 71, "ph": "X", "name": "ReadAsync 1174", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640447, "dur": 2, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640451, "dur": 44, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640497, "dur": 1, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640499, "dur": 34, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640536, "dur": 30, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640569, "dur": 64, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640637, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640639, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640667, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640669, "dur": 42, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640714, "dur": 43, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640759, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640761, "dur": 36, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640800, "dur": 27, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640831, "dur": 28, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640861, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640862, "dur": 28, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640893, "dur": 34, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640930, "dur": 33, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640966, "dur": 28, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167640997, "dur": 53, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641052, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641053, "dur": 25, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641080, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641123, "dur": 37, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641163, "dur": 28, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641193, "dur": 34, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641231, "dur": 29, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641262, "dur": 27, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641292, "dur": 31, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641327, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641329, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641381, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641383, "dur": 56, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641442, "dur": 13, "ph": "X", "name": "ProcessMessages 1063", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641457, "dur": 65, "ph": "X", "name": "ReadAsync 1063", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641524, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641526, "dur": 48, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641577, "dur": 2, "ph": "X", "name": "ProcessMessages 1085", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641580, "dur": 48, "ph": "X", "name": "ReadAsync 1085", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641631, "dur": 2, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641634, "dur": 52, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641690, "dur": 1, "ph": "X", "name": "ProcessMessages 1331", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641692, "dur": 44, "ph": "X", "name": "ReadAsync 1331", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641739, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167641741, "dur": 1795, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167643542, "dur": 2, "ph": "X", "name": "ProcessMessages 1019", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167643545, "dur": 405, "ph": "X", "name": "ReadAsync 1019", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167643954, "dur": 18, "ph": "X", "name": "ProcessMessages 20514", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167643972, "dur": 35, "ph": "X", "name": "ReadAsync 20514", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644010, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644015, "dur": 53, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644071, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644073, "dur": 41, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644116, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644118, "dur": 62, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644183, "dur": 1, "ph": "X", "name": "ProcessMessages 931", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644185, "dur": 35, "ph": "X", "name": "ReadAsync 931", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644224, "dur": 258, "ph": "X", "name": "ReadAsync 5", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644486, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644488, "dur": 49, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644540, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644541, "dur": 53, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644597, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644599, "dur": 37, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644639, "dur": 36, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644677, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644679, "dur": 43, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644724, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644726, "dur": 44, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644773, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644775, "dur": 51, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644829, "dur": 1, "ph": "X", "name": "ProcessMessages 1104", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644831, "dur": 47, "ph": "X", "name": "ReadAsync 1104", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644881, "dur": 1, "ph": "X", "name": "ProcessMessages 869", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644884, "dur": 54, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644940, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644942, "dur": 46, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644990, "dur": 1, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167644992, "dur": 45, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167645039, "dur": 1, "ph": "X", "name": "ProcessMessages 785", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167645041, "dur": 49, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167645093, "dur": 1, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167645095, "dur": 41, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167645139, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167645141, "dur": 50, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167645193, "dur": 1, "ph": "X", "name": "ProcessMessages 1151", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167645196, "dur": 46, "ph": "X", "name": "ReadAsync 1151", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167645244, "dur": 1, "ph": "X", "name": "ProcessMessages 928", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167645246, "dur": 52, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167645301, "dur": 1, "ph": "X", "name": "ProcessMessages 1062", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167645304, "dur": 46, "ph": "X", "name": "ReadAsync 1062", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167645353, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167645355, "dur": 40, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167645398, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167645401, "dur": 48, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167645452, "dur": 1, "ph": "X", "name": "ProcessMessages 984", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167645454, "dur": 45, "ph": "X", "name": "ReadAsync 984", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167645502, "dur": 1, "ph": "X", "name": "ProcessMessages 899", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167645505, "dur": 36, "ph": "X", "name": "ReadAsync 899", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167645543, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167645545, "dur": 439, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167645989, "dur": 366, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167646359, "dur": 466, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167646829, "dur": 58, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167646891, "dur": 4, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167646897, "dur": 32, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167646932, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167646934, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167646963, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167646966, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167646990, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167646992, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647016, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647018, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647040, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647068, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647087, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647107, "dur": 19, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647127, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647163, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647190, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647194, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647243, "dur": 22, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647269, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647301, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647328, "dur": 19, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647350, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647368, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647389, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647424, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647445, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647560, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647583, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647606, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647628, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647650, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647692, "dur": 102, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647797, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647800, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647846, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647849, "dur": 31, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647882, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647884, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647931, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647967, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167647990, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648034, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648036, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648064, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648087, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648110, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648246, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648248, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648287, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648289, "dur": 22, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648315, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648342, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648373, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648398, "dur": 20, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648420, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648422, "dur": 27, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648453, "dur": 24, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648479, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648480, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648502, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648504, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648527, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648529, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648551, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648573, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648595, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648616, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648639, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648662, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648683, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648716, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648741, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648763, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648795, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648798, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648840, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648842, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648863, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648865, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648888, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648910, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648932, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648973, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167648976, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649009, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649011, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649035, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649062, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649096, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649098, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649126, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649148, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649171, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649208, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649210, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649241, "dur": 5, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649247, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649290, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649292, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649320, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649344, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649381, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649383, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649417, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649441, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649463, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649486, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649508, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649538, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649560, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649583, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649610, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649634, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649659, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649681, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649700, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167649722, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167650805, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167650813, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167650878, "dur": 3, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167650883, "dur": 8431, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167659325, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167659329, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167659403, "dur": 5, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167659409, "dur": 41, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167659455, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167659459, "dur": 39, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167659505, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167659539, "dur": 1010, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167660555, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167660559, "dur": 1290, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167661856, "dur": 65, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167661923, "dur": 41, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167661967, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167662060, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167662063, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167662098, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167662101, "dur": 501, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167662609, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167662612, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167662648, "dur": 24, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167662673, "dur": 113, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167662789, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167662791, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167662814, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167662816, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167662910, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167662931, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167662932, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663023, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663056, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663058, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663118, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663145, "dur": 23, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663170, "dur": 128, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663302, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663325, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663346, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663368, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663407, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663427, "dur": 120, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663550, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663571, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663572, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663639, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663641, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663682, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663751, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663783, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663784, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663872, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663874, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663909, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663911, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167663988, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167664022, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167664023, "dur": 351, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167664380, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167664383, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167664431, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167664433, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167664464, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167664466, "dur": 188, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167664658, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167664661, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167664704, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167664707, "dur": 151, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167664863, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167664896, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167664948, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167664951, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167664973, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167665011, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167665033, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167665149, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167665152, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167665177, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167665179, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167665301, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167665303, "dur": 106, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167665412, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167665414, "dur": 152, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167665569, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167665572, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167665598, "dur": 209, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167665813, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167665816, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167665854, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167665859, "dur": 600, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167666467, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167666473, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167666523, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167666526, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167666625, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167666652, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167666654, "dur": 215, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167666874, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167666876, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167666921, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167666923, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167666957, "dur": 20, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167666979, "dur": 95, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167667078, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167667105, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167667185, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167667221, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167667223, "dur": 118, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167667345, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167667347, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167667391, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167667393, "dur": 123, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167667520, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167667547, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167667580, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167667582, "dur": 183, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167667769, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167667791, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167667859, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167667911, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167667947, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167667949, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167667982, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668007, "dur": 2, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668010, "dur": 41, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668055, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668082, "dur": 23, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668107, "dur": 31, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668141, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668179, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668181, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668204, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668206, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668251, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668280, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668283, "dur": 240, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668527, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668551, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668572, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668665, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668686, "dur": 21, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668709, "dur": 34, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668744, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668746, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668777, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668800, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668821, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668823, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668872, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668892, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167668894, "dur": 395, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167669293, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167669296, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167669333, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167669335, "dur": 47, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167669386, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167669410, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167669436, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167669457, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167669479, "dur": 14, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167669495, "dur": 31, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167669530, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167669601, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167669603, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167669628, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167669632, "dur": 181, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167669819, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167669857, "dur": 121, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167669982, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167670005, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167670112, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167670137, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167670140, "dur": 1051, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167671200, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167671204, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167671238, "dur": 33, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167671273, "dur": 116, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167671393, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167671395, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167671424, "dur": 13, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167671439, "dur": 65, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167671518, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167671521, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167671583, "dur": 13, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167671598, "dur": 32, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167671633, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167671635, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167671674, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167671711, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167671714, "dur": 233, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167671951, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167671953, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167672040, "dur": 1, "ph": "X", "name": "ProcessMessages 29", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167672042, "dur": 35, "ph": "X", "name": "ReadAsync 29", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167672080, "dur": 22, "ph": "X", "name": "ProcessMessages 51", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167672103, "dur": 53, "ph": "X", "name": "ReadAsync 51", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167672160, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167672183, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167672185, "dur": 30, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167672219, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167672243, "dur": 10, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167672254, "dur": 220, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167672479, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167672481, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167672508, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167672511, "dur": 798, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167673317, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167673321, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167673351, "dur": 26, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167673378, "dur": 175, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167673560, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167673562, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167673605, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167673609, "dur": 458, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167674072, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167674074, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167674124, "dur": 31, "ph": "X", "name": "ProcessMessages 75", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167674157, "dur": 39, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167674200, "dur": 16, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167674218, "dur": 78, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167674300, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167674325, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167674328, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167674351, "dur": 11, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167674363, "dur": 67, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167674433, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167674455, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167674457, "dur": 84, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167674545, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167674547, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167674573, "dur": 11, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167674585, "dur": 70, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167674659, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167674661, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167674699, "dur": 13, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167674714, "dur": 31, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167674747, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167674749, "dur": 748, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167675507, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167675511, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167675564, "dur": 21, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167675587, "dur": 54, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167675645, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167675678, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167675681, "dur": 354, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676039, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676042, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676087, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676114, "dur": 32, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676149, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676153, "dur": 105, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676262, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676288, "dur": 11, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676301, "dur": 70, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676376, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676378, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676406, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676409, "dur": 82, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676495, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676524, "dur": 11, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676536, "dur": 49, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676590, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676614, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676617, "dur": 32, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676653, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676681, "dur": 11, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676694, "dur": 20, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676720, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676724, "dur": 171, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676899, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676927, "dur": 11, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676940, "dur": 28, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676971, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676994, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167676996, "dur": 698, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167677700, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167677703, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167677741, "dur": 40, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167677784, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167677823, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167677826, "dur": 282, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167678114, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167678172, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167678174, "dur": 36, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167678223, "dur": 20, "ph": "X", "name": "ProcessMessages 6", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167678246, "dur": 143, "ph": "X", "name": "ReadAsync 6", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167678401, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167678403, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167678433, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167678436, "dur": 140, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167678582, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167678650, "dur": 14, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167678673, "dur": 44, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167678720, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167678724, "dur": 327, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167679055, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167679058, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167679080, "dur": 23, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167679105, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167679134, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167679137, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167679170, "dur": 9, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167679180, "dur": 34, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167679216, "dur": 8, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167679226, "dur": 670, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167679903, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167679906, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167679951, "dur": 20, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167679972, "dur": 313, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167680291, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167680294, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167680324, "dur": 44, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167680370, "dur": 25, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167680396, "dur": 11, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167680409, "dur": 92, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167680506, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167680535, "dur": 10, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167680547, "dur": 129452, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167810012, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167810026, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167810100, "dur": 35, "ph": "X", "name": "ProcessMessages 11237", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167810137, "dur": 8305, "ph": "X", "name": "ReadAsync 11237", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167818448, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167818450, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167818482, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 17020, "tid": 34359738368, "ts": 1750355167818484, "dur": 7082, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 17020, "tid": 16, "ts": 1750355167830519, "dur": 3635, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 17020, "tid": 30064771072, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 17020, "tid": 30064771072, "ts": 1750355167608579, "dur": 56793, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 17020, "tid": 30064771072, "ts": 1750355167665374, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 17020, "tid": 30064771072, "ts": 1750355167665375, "dur": 105, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 17020, "tid": 16, "ts": 1750355167834158, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 17020, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 17020, "tid": 25769803776, "ts": 1750355167603451, "dur": 222172, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 17020, "tid": 25769803776, "ts": 1750355167603860, "dur": 4671, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 17020, "tid": 25769803776, "ts": 1750355167825634, "dur": 66, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 17020, "tid": 25769803776, "ts": 1750355167825651, "dur": 17, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 17020, "tid": 25769803776, "ts": 1750355167825702, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 17020, "tid": 16, "ts": 1750355167834165, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750355167632530, "dur": 69, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750355167632627, "dur": 2330, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750355167634970, "dur": 270, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750355167635324, "dur": 264, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750355167635699, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_1F322CB3825CC582.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750355167636169, "dur": 787, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_02EB6148B760FC69.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750355167636960, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_7417C209D47BDBE3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750355167637092, "dur": 114, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_CB9C52C6DA4AB85A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750355167637207, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_1E51E4CC49144E2E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750355167637517, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_5592C7A2A0C2A571.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750355167637681, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_42F7D41B93B94AC7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750355167637834, "dur": 448, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_33A63B48735421F9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750355167638473, "dur": 188, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D0F1A61EB4189FC9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750355167638733, "dur": 200, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_C44092216EEC6649.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750355167642635, "dur": 292, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1750355167643024, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750355167643283, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750355167643770, "dur": 688, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750355167644635, "dur": 332, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750355167635612, "dur": 10410, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750355167646029, "dur": 170079, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750355167816145, "dur": 179, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750355167816444, "dur": 166, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750355167818855, "dur": 50, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750355167818932, "dur": 1984, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750355167636052, "dur": 9992, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750355167646072, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_A9C666157FD2E5AB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750355167646427, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750355167646478, "dur": 346, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_A9C666157FD2E5AB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750355167646963, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750355167647393, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BD985A39D270C9B7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750355167647894, "dur": 581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_4FF2CB3C17FB8E73.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750355167648520, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_4FF2CB3C17FB8E73.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750355167648579, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_9F4733395C159688.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750355167648657, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750355167648750, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750355167649028, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750355167649172, "dur": 10341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750355167659515, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750355167659696, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750355167659873, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750355167660002, "dur": 874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750355167660879, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750355167661082, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750355167661272, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750355167661357, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750355167661887, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750355167662103, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750355167662351, "dur": 856, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750355167663208, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750355167663403, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_E8EFDB1B8D5C174E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750355167663515, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750355167663744, "dur": 1102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750355167664846, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750355167664925, "dur": 2295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750355167667222, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750355167667416, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750355167667555, "dur": 452, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750355167668027, "dur": 910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750355167668938, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750355167669051, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750355167669192, "dur": 661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750355167669854, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750355167670005, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750355167670125, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750355167670311, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750355167670481, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750355167672456, "dur": 90, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750355167670622, "dur": 1933, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Interfaces.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750355167672562, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750355167672663, "dur": 2174, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750355167674844, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750355167674952, "dur": 1574, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750355167676604, "dur": 1606, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Splines.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1750355167678257, "dur": 1400, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1750355167679659, "dur": 135950, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750355167815610, "dur": 516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750355167636209, "dur": 9890, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750355167646110, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_1A8907DE4FA741CC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750355167646420, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750355167646574, "dur": 433, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_8D8B6A3723BAC276.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750355167647148, "dur": 460, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750355167647878, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_97DB01ADA77F4791.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750355167648242, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750355167648439, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_F5DF255C08D11A8C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750355167648558, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_A4F1D8B2044EDCB1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750355167648822, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_A4F1D8B2044EDCB1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750355167649024, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750355167649201, "dur": 8422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750355167657625, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750355167658544, "dur": 3229, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750355167661775, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750355167662038, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750355167662175, "dur": 919, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1750355167663098, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750355167663278, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750355167663759, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750355167663910, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750355167664053, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750355167664122, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750355167664239, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750355167664360, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Abilities.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750355167664471, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750355167664851, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750355167665121, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Actions.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750355167665290, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750355167665365, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750355167665444, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750355167665503, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750355167665638, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750355167665887, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750355167666067, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750355167668747, "dur": 417, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750355167666306, "dur": 2887, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Battle.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750355167669198, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750355167669307, "dur": 2704, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750355167672055, "dur": 1737, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Cinemachine.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1750355167673803, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750355167674040, "dur": 1936, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Splines.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1750355167675986, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750355167676138, "dur": 2050, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1750355167678196, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750355167678261, "dur": 1420, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750355167679683, "dur": 136413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167636228, "dur": 9886, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167646125, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_E3D9DEC668D47C33.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750355167646513, "dur": 357, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_E3D9DEC668D47C33.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750355167646935, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_EF62AA7CD312F942.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750355167647544, "dur": 506, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167648057, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_BEDC949A65E52DA7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750355167648181, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167648484, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_BEDC949A65E52DA7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750355167648729, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167648795, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1750355167648968, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750355167649033, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167649219, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167649318, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750355167649395, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167649724, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167649801, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1750355167650022, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1750355167650214, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750355167650446, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167650550, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167650699, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167651850, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167652614, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167653100, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167653566, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167654070, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167654567, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167655022, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167655422, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167655876, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167656543, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167657484, "dur": 562, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.2.0b2\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750355167657254, "dur": 1142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167658397, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167659137, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167660046, "dur": 2200, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1750355167662249, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750355167662365, "dur": 1445, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167663854, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750355167664366, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167664479, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750355167664919, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167665119, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750355167665758, "dur": 218, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750355167668851, "dur": 402, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b2/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 3, "ts": 1750355167665977, "dur": 3276, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167669257, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167669369, "dur": 2295, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Splines.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750355167671675, "dur": 468, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167672166, "dur": 2437, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750355167674612, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167674808, "dur": 2195, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Splines.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750355167677018, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750355167677084, "dur": 2470, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Battle.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1750355167679602, "dur": 1259, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1750355167680864, "dur": 135252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167636092, "dur": 9978, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167646097, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_686B6CE4ABC74178.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750355167646502, "dur": 356, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_686B6CE4ABC74178.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750355167646860, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_B701B6B92B8026DD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750355167646959, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_163A7D00BA7D065C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750355167647062, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_564C15F1D50DDD04.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750355167647583, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_33A63B48735421F9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750355167648097, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167648185, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_7417C209D47BDBE3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750355167648281, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_747257F2B95C7621.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750355167648455, "dur": 389, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_747257F2B95C7621.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750355167648938, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750355167649234, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167649405, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167649567, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167649666, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167649822, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167649876, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1750355167649981, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1750355167650070, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167650191, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750355167650298, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167650637, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167651839, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167652751, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167653454, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167654138, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167654809, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167655443, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167656112, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167656858, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167657518, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167658304, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167659173, "dur": 516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167659689, "dur": 1423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167661112, "dur": 925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167662041, "dur": 485, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167668493, "dur": 63, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167662548, "dur": 6019, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750355167668649, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167668767, "dur": 3103, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Cinemachine.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750355167674552, "dur": 63, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167671916, "dur": 2706, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Cinemachine.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750355167674670, "dur": 2097, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Cinemachine.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1750355167676775, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167678596, "dur": 71, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167676868, "dur": 1873, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1750355167678748, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750355167678878, "dur": 1491, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Interfaces.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1750355167680406, "dur": 135760, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167636178, "dur": 9907, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167646097, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_E0FCC25CAA3CC2C8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750355167646534, "dur": 353, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_7614754D03CD3CB4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750355167646889, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_2E76C9AC399D6AFC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750355167646963, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167647016, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_D437D5B52E940013.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750355167647459, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167647700, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_9283034F04690CF9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750355167647757, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167647835, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_47DDA486F86DF89C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750355167648292, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_416ADE92277E457E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750355167648412, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_416ADE92277E457E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750355167649104, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167649326, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167649439, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167649504, "dur": 406, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167649921, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1750355167650080, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167650661, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167651383, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167652333, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167653055, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167653794, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167654852, "dur": 1084, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\uint3x4.gen.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750355167654261, "dur": 1730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167655991, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167656793, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167657461, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167657867, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167658736, "dur": 71, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167658808, "dur": 56, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167658875, "dur": 126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167659002, "dur": 705, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167659708, "dur": 1385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167661098, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167661227, "dur": 2473, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750355167663703, "dur": 1180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Status.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750355167664936, "dur": 2302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Status.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750355167667240, "dur": 581, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167667828, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750355167668260, "dur": 197, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750355167668660, "dur": 141962, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750355167636340, "dur": 9883, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750355167646224, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_988D394CD4FBAB22.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750355167646559, "dur": 423, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_988D394CD4FBAB22.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750355167646984, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_16F59DAED9B12573.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750355167647096, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_D1622255EC84C8DF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750355167647210, "dur": 842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_37A91187EFAA1E4D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750355167648053, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750355167648412, "dur": 494, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_916E5E16AB469CF0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750355167648908, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750355167649149, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1750355167649384, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750355167649726, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750355167650648, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750355167651212, "dur": 972, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@ed7d081a6a9c\\Editor\\TMP\\PropertyDrawers\\TMP_PropertyDrawerUtilities.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750355167651139, "dur": 1645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750355167652784, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750355167653484, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750355167654287, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750355167654949, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750355167655587, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750355167656238, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750355167657144, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750355167659783, "dur": 2019, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1750355167661804, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750355167662054, "dur": 1535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750355167663591, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750355167663733, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750355167663812, "dur": 3378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750355167667192, "dur": 471, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750355167667750, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750355167668094, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750355167668277, "dur": 882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750355167669160, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750355167669281, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Abilities.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750355167669692, "dur": 78, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750355167669864, "dur": 140707, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Abilities.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750355167815601, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167636265, "dur": 9862, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167646133, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_93BF4D5398025317.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750355167646472, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167646560, "dur": 437, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_93BF4D5398025317.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750355167646999, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9B5BF5B045BFFED8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750355167647501, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_C368AB8F525C9F58.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750355167647898, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_A8E13D05E0B11793.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750355167648366, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_E90840FC7BA4DF8A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750355167648467, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_E90840FC7BA4DF8A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750355167648526, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_BD066AF91216C895.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750355167649663, "dur": 255, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750355167649921, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167649984, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167650137, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167650434, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167650511, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750355167650760, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167650830, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167651592, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167652537, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167653288, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167654006, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167654679, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167655373, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167656088, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167656637, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167657136, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167657669, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167658386, "dur": 968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167659355, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167659674, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167659843, "dur": 50, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167659895, "dur": 2157, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750355167662054, "dur": 1731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167663786, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750355167664049, "dur": 837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750355167664888, "dur": 2013, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167666923, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167667119, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750355167667366, "dur": 850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750355167668217, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167668353, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750355167668412, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750355167669205, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167669284, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750355167669768, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167669940, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167672431, "dur": 109, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167670085, "dur": 2472, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Core.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750355167672563, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750355167672659, "dur": 2514, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750355167675226, "dur": 1931, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Status.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750355167677211, "dur": 1869, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750355167679115, "dur": 1664, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750355167680783, "dur": 135331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167636298, "dur": 9897, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167646200, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4B6EE49D6AF37933.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750355167646545, "dur": 351, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4B6EE49D6AF37933.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750355167646961, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_CE52468F2AF94D3E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750355167647443, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167647767, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_2DC85C64375951B1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750355167647911, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_1E51E4CC49144E2E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750355167648052, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167648276, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167648475, "dur": 405, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_31E341F8DD700DC8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750355167648882, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750355167649122, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167649214, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167649511, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167649644, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167649752, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167649819, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167649884, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1750355167649942, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167650023, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1750355167650610, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167651281, "dur": 949, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167652230, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167652814, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167653700, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167654174, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167654643, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167655113, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167655533, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167656075, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167656539, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167657402, "dur": 1498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167658900, "dur": 813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167659713, "dur": 1376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167661093, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167661225, "dur": 6197, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1750355167667426, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750355167667572, "dur": 672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750355167668245, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167668480, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750355167668688, "dur": 1293, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Status.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750355167669985, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167670088, "dur": 2635, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750355167672729, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167672968, "dur": 2093, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Core.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1750355167675066, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750355167675153, "dur": 2249, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1750355167677468, "dur": 2106, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750355167679631, "dur": 1378, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1750355167681011, "dur": 135116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750355167823908, "dur": 1466, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 17020, "tid": 16, "ts": 1750355167834238, "dur": 2427, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 17020, "tid": 16, "ts": 1750355167836791, "dur": 1850, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 17020, "tid": 16, "ts": 1750355167830487, "dur": 8202, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}